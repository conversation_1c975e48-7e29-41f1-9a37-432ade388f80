import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@payload-config'
import { 
  contactSubmissionsHandler, 
  createContactSubmissionHandler 
} from '@/endpoints/contact-submissions'

export async function GET(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    
    // Create a mock request object that matches PayloadRequest interface
    const mockReq = {
      payload,
      query: Object.fromEntries(request.nextUrl.searchParams.entries()),
      headers: {
        get: (name: string) => request.headers.get(name),
      },
      user: null, // No user for public access
    } as any

    // Create a mock response object
    let responseData: any
    let statusCode = 200

    const mockRes = {
      status: (code: number) => {
        statusCode = code
        return mockRes
      },
      json: (data: any) => {
        responseData = data
        return mockRes
      },
    } as any

    // Call the handler
    await contactSubmissionsHandler(mockReq, mockRes)

    return NextResponse.json(responseData, { status: statusCode })
  } catch (error) {
    console.error('Contact submissions GET error:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    const body = await request.json()
    
    // Create a mock request object that matches PayloadRequest interface
    const mockReq = {
      payload,
      body,
      query: Object.fromEntries(request.nextUrl.searchParams.entries()),
      headers: {
        get: (name: string) => request.headers.get(name),
        'user-agent': request.headers.get('user-agent'),
        'x-forwarded-for': request.headers.get('x-forwarded-for'),
        referer: request.headers.get('referer'),
        referrer: request.headers.get('referrer'),
      },
      ip: request.headers.get('x-forwarded-for') || 
          request.headers.get('x-real-ip') || 
          '127.0.0.1',
      connection: {
        remoteAddress: request.headers.get('x-forwarded-for') || '127.0.0.1'
      },
      user: null, // No user for public submissions
    } as any

    // Create a mock response object
    let responseData: any
    let statusCode = 200

    const mockRes = {
      status: (code: number) => {
        statusCode = code
        return mockRes
      },
      json: (data: any) => {
        responseData = data
        return mockRes
      },
    } as any

    // Call the handler
    await createContactSubmissionHandler(mockReq, mockRes)

    return NextResponse.json(responseData, { status: statusCode })
  } catch (error) {
    console.error('Contact submissions POST error:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: 'Failed to submit contact form'
      },
      { status: 500 }
    )
  }
}
